<template>
  <div ref="wrap" class="g6-wrap"></div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref, watch } from "vue";
import { Graph } from "@antv/g6";

const props = defineProps({
  nodes: { type: Array, required: true }, // [{id, nodeName, label, entityId, ...}]
  links: { type: Array, required: true }, // [{id, source, target, name}]
  height: { type: Number, default: 680 },
});

const wrap = ref(null);
let graph;

// 颜色映射（可按需修改成你的配色）
const palette = {
  军事人员表: "#3FA7FF",
  军事训练表: "#22CDB5",
  军事作战单位表: "#FFC145",
};
const fallbackColors = ["#5B8FF9", "#61DDAA", "#65789B", "#F6BD16", "#7262fd", "#78D3F8"];
const pickColor = (type, i = 0) => palette[type] || fallbackColors[i % fallbackColors.length];

// 将你的数据转成 G6 需要的格式
function toG6Data(nodes, links) {
  const gNodes = nodes.map((n, i) => ({
    id: String(n.id),
    label: n.nodeName || n.id,
    data: n,
    style: {
      fill: pickColor(n.label, i),
      stroke: "#fff",
      lineWidth: 2,
      // 发光（光晕）效果
      halo: {
        // G6 v5: 在 state 样式里更灵活，这里给个默认
        lineWidth: 12,
        stroke: pickColor(n.label, i),
        opacity: 0.15,
      },
      // 节点尺寸
      size: 32,
      // 标签样式
      labelText: n.nodeName || n.id,
      labelFill: "#333",
      labelFontSize: 12,
      labelPlacement: "bottom",
      labelMaxLines: 1,
      labelPadding: [4, 6, 2, 6],
    },
  }));
  const gEdges = links.map((e, i) => ({
    id: String(e.id ?? `${e.source}-${e.target}-${i}`),
    source: String(e.source),
    target: String(e.target),
    data: e,
    type: "quadratic", // 弧线
    style: {
      stroke: "#2d9cdb",
      lineWidth: 3,
      endArrow: true,
      // 弧度（正负决定弯曲方向），多条边可用不同 offset 做分离
      curveOffset: 24 + (i % 3) * 8,
      // 边文字（带圆角背景，类似“胶囊”）
      labelText: e.name || "",
      labelBackground: true,
      labelBackgroundFill: "#ffffff",
      labelBackgroundRadius: 6,
      labelBackgroundStroke: "#b9d7f4",
      labelBackgroundLineWidth: 1,
      labelPadding: [2, 6, 2, 6],
      labelFontSize: 12,
      labelFill: "#2f76c3",
      labelAutoRotate: true,
    },
  }));
  return { nodes: gNodes, edges: gEdges };
}

function init() {
  const { clientWidth } = wrap.value;
  const { nodes, edges } = toG6Data(props.nodes, props.links);

  graph = new Graph({
    container: wrap.value,
    autoFit: "center", // 初次居中
    width: clientWidth,
    height: props.height,
    background: "transparent",
    // 交互
    modes: {
      default: [
        "drag-canvas",
        "zoom-canvas",
        "drag-element", // 节点拖拽
        {
          type: "click-element", // 点击高亮邻居
          onClick: ({ target }) => {
            const id = target?.id;
            if (!id) {
              clearActive();
              return;
            }
            setActive(id);
          },
        },
        {
          type: "hover-element", // tooltip
          onHover: ({ target, event }) => {
            if (target?.type === "node") {
              const data = graph.getNodeData(target.id);
              showTooltip(event.canvas.x, event.canvas.y, data);
            }
          },
          onLeave: hideTooltip,
        },
      ],
    },
    node: {
      type: "circle",
      style: {
        size: 32,
      },
      state: {
        active: {
          lineWidth: 3,
          stroke: "#111",
          // 更强的光晕
          halo: {
            lineWidth: 18,
            opacity: 0.25,
          },
        },
        inactive: {
          opacity: 0.2,
          labelOpacity: 0.2,
        },
      },
    },
    edge: {
      type: "quadratic",
      style: {
        lineWidth: 3,
        stroke: "#8aa4c4",
        endArrow: true,
      },
      state: {
        active: {
          stroke: "#2ca02c",
          lineWidth: 3.5,
          opacity: 1,
          labelOpacity: 1,
        },
        inactive: {
          opacity: 0.15,
          labelOpacity: 0.2,
        },
      },
    },
    // 力导向布局（你也可以换成 'dagre' 看看）
    layout: {
      type: "force",
      preventOverlap: true,
      linkDistance: 120,
      nodeStrength: -400,
      edgeStrength: 0.6,
      collideStrength: 0.8,
      alpha: 0.3,
    },
    data: { nodes, edges },
  });

  graph.render();

  // 自适应
  window.addEventListener("resize", onResize);
}

// —— 选中/高亮 —— //
function clearActive() {
  graph.getNodesData().forEach((n) => graph.setElementState(n.id, "inactive", false));
  graph.getEdgesData().forEach((e) => graph.setElementState(e.id, "inactive", false));
  graph.getNodesData().forEach((n) => graph.setElementState(n.id, "active", false));
  graph.getEdgesData().forEach((e) => graph.setElementState(e.id, "active", false));
}

function setActive(centerId) {
  const neighbors = new Set([centerId]);
  graph.getEdgesData().forEach((e) => {
    if (e.source === centerId) neighbors.add(e.target);
    if (e.target === centerId) neighbors.add(e.source);
  });

  // 先全部置“未激活”
  graph.getNodesData().forEach((n) => graph.setElementState(n.id, "inactive", !neighbors.has(n.id)));
  graph.getEdgesData().forEach((e) => {
    const act = e.source === centerId || e.target === centerId;
    graph.setElementState(e.id, "inactive", !act);
  });

  // 目标节点 & 邻边置“激活”
  graph.setElementState(centerId, "active", true);
  graph.getEdgesData().forEach((e) => {
    if (e.source === centerId || e.target === centerId) {
      graph.setElementState(e.id, "active", true);
      graph.setElementState(e.source, "active", true);
      graph.setElementState(e.target, "active", true);
    }
  });
}

// —— 简易 tooltip —— //
let tt;
function ensureTooltip() {
  if (tt) return tt;
  tt = document.createElement("div");
  tt.className = "g6-tooltip";
  wrap.value.appendChild(tt);
  return tt;
}
function showTooltip(x, y, data) {
  const el = ensureTooltip();
  el.style.display = "block";
  el.style.left = `${x + 12}px`;
  el.style.top = `${y + 12}px`;
  el.innerHTML = `
    <div class="tt-name">${data?.data?.nodeName ?? data?.label ?? data?.id}</div>
    ${data?.data?.entityId ? `<div class="tt-sub">ID：${data.data.entityId}</div>` : ""}
    ${data?.data?.label ? `<div class="tt-sub">类型：${data.data.label}</div>` : ""}
  `;
}
function hideTooltip() {
  if (tt) tt.style.display = "none";
}

function onResize() {
  if (!graph || !wrap.value) return;
  const w = wrap.value.clientWidth;
  graph.setSize(w, props.height);
}

onMounted(() => init());
onBeforeUnmount(() => {
  window.removeEventListener("resize", onResize);
  if (graph) graph.destroy();
});

// 数据变化时更新（可按需做 diff，这里直接 changeData）
watch(
  () => [props.nodes, props.links],
  () => {
    if (!graph) return;
    clearActive();
    const { nodes, edges } = toG6Data(props.nodes, props.links);
    graph.changeData({ nodes, edges });
  },
  { deep: true }
);
</script>

<style scoped>
.g6-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 480px;
  background: radial-gradient(ellipse at bottom right, rgba(0, 0, 0, 0.04), transparent 60%);
}

.g6-tooltip {
  position: absolute;
  display: none;
  background: rgba(20, 24, 36, 0.92);
  color: #fff;
  padding: 8px 10px;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.18);
  font-size: 12px;
  pointer-events: none;
  max-width: 240px;
}
.tt-name {
  font-weight: 600;
  margin-bottom: 2px;
}
.tt-sub {
  opacity: 0.9;
}
</style>
